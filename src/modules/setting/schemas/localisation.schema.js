import { COMMON_STATUSES, MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import {
  OFFSET_PAGINATION_QUERY_PARAMS,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { LocalisationConstant } from '#src/modules/setting/constants/index.js';

const { COMMON_PROPERTIES, ERROR_RESPONSE, REQ_PARAM_UUID, UPDATE_RESPONSE, VIEW_RESPONSE } =
  CoreSchema;
const { LOCALISATION_CATEGORIES } = LocalisationConstant;
const { LOCALISATION } = MODULE_NAMES;

const TAGS = ['BO / Settings / Currency, Language, Regions'];

/**
 * Response schema properties for localisation entity.
 */
const LOCALISATION_COMMON_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  entityId: { type: 'string', format: 'uuid' },
  category: { type: 'string', enum: Object.values(LOCALISATION_CATEGORIES) },
  name: { type: 'string' },
  code: { type: 'string', maxLength: 10 },
  status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
  version: { type: 'integer' },
  metadata: { type: 'object', additionalProperties: true },
};

const LOCALISATION_RES_PROPERTIES = {
  if: {
    type: 'object',
    properties: {
      category: { const: 'currency' },
    },
  },
  then: {
    type: 'object',
    properties: {
      ...LOCALISATION_COMMON_RES_PROPERTIES,
      baseCurrency: { type: 'string', maxLength: 10 },
      exchangeRate: { type: 'number' },
    },
  },
  else: {
    type: 'object',
    properties: LOCALISATION_COMMON_RES_PROPERTIES,
  },
};

/**
 * List endpoint schema for localisation.
 */
export const index = {
  tags: TAGS,
  summary: `Get a list of ${LOCALISATION}`,
  querystring: {
    type: 'object',
    properties: {
      'filter_localisation.category_eq': {
        type: 'string',
        enum: Object.values(LOCALISATION_CATEGORIES),
      },
      'filter_localisation.code_eq': { type: 'string' },
      'filter_localisation.name_iLike': { type: 'string' },
      'filter_localisation.status_eq': {
        oneOf: [
          {
            type: 'array',
            items: { type: 'string' },
            uniqueItems: true,
            minItems: 1,
          },
          { type: 'string', enum: Object.values(COMMON_STATUSES) },
        ],
      },
      ...OFFSET_PAGINATION_QUERY_PARAMS,
      sortBy: {
        oneOf: [{ type: 'string' }, { type: 'array' }],
        description: 'Sorting field in the format (field:order)',
        default: 'localisation.name:asc',
      },
    },
    required: ['filter_localisation.category_eq'],
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: LOCALISATION_RES_PROPERTIES,
        },
        meta: createOffsetPaginationResponseSchema(),
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * View endpoint schema for localisation.
 */
export const view = {
  tags: TAGS,
  summary: `View a ${LOCALISATION}`,
  params: REQ_PARAM_UUID,
  response: VIEW_RESPONSE(LOCALISATION_RES_PROPERTIES),
};

/**
 * Update exchange rate schema.
 */
export const update = {
  tags: TAGS,
  summary: `Update a ${LOCALISATION}`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      version: { type: 'number', default: 1 },
      exchangeRate: { type: 'number' },
      category: { type: 'string', enum: Object.values(LOCALISATION_CATEGORIES) },
    },
    required: ['version'],
    if: {
      properties: {
        category: { const: 'currency' },
      },
    },
    then: {
      required: ['version', 'exchangeRate'],
    },
    else: {
      required: ['version'],
    },
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update status schema for localisation.
 */
export const updateStatus = {
  tags: TAGS,
  summary: `Update a ${LOCALISATION} status`,
  params: REQ_PARAM_UUID,
  body: {
    type: 'object',
    properties: {
      status: { type: 'string', enum: Object.values(COMMON_STATUSES) },
      version: { type: 'number', default: 1 },
    },
    required: ['version', 'status'],
  },
  response: UPDATE_RESPONSE,
};
